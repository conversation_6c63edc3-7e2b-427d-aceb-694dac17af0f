import { captureException, captureMessage } from "@sentry/node";
import { fees, investmentUniverseConfig, entitiesConfig, whitelistConfig } from "@wealthyhood/shared-configs";
import { ApiResponse, PaginatedRewardsResponse } from "apiResponse";
import Decimal from "decimal.js";
import { RewardsFilter } from "filters";
import gaussian from "gaussian";
import mongoose, { QueryOptions, Types } from "mongoose";
import validator from "validator";
import { MainCurrencyToWealthkernelCurrency } from "../configs/currenciesConfig";
import { ProviderEnum } from "../configs/providersConfig";
import events from "../event-handlers/events";
import logger from "../external-services/loggerService";
import OrderService from "./orderService";
import MailchimpService, { AudienceIdEnum } from "../external-services/mailchimpService";
import { TrackPropertiesType, TrackTransactionInfoType } from "../external-services/segmentAnalyticsService";
import {
  BonusStatusType,
  OrderSideType,
  WealthkernelOrderStatusWithSettledType
} from "../external-services/wealthkernelService";
import eventEmitter from "../loaders/eventEmitter";
import { RedisClientService } from "../loaders/redis";
import { InternalServerError } from "../models/ApiErrors";
import { Reward, RewardDocument, RewardDTOInterface, RewardPopulationFieldsEnum } from "../models/Reward";
import { SubscriptionDocument } from "../models/Subscription";
import { KycStatusEnum, User, UserDocument } from "../models/User";
import ConfigUtil from "../utils/configUtil";
import DateUtil from "../utils/dateUtil";
import DbUtil from "../utils/dbUtil";
import PaginationUtil from "../utils/paginationUtil";
import { calculateBrokerFxFee, calculateDisplayFxFee } from "../utils/feesUtil";
import InvestmentProductService from "./investmentProductService";
import PortfolioService from "./portfolioService";
import ProviderService, { ProviderScopeEnum } from "./providerService";
import UserService from "./userService";
import { UserRepository } from "../repositories/userRepository";
import SubmissionWindowUtil from "../utils/submissionWindowUtil";

const { ASSET_CONFIG } = investmentUniverseConfig;
const { MINIMUM_FX_FEE, MINIMUM_COMMISSION_FEE, EXECUTION_SPREAD_RATES } = fees;
const REWARD_ASSETS: investmentUniverseConfig.AssetType[] = [
  "equities_apple",
  "equities_airbnb",
  "equities_amazon",
  "equities_blackrock",
  "equities_berkshire_hathaway",
  "equities_coinbase",
  "equities_salesforce",
  "equities_dropbox",
  "equities_disney",
  "equities_alphabet",
  "equities_goldman_sachs",
  "equities_hubspot",
  "equities_coca_cola",
  "equities_mastercard",
  "equities_mcdonalds",
  "equities_meta",
  "equities_microsoft",
  "equities_netflix",
  "equities_nike",
  "equities_nvidia",
  "equities_pfizer",
  "equities_palantir_technologies",
  "equities_paypal",
  "equities_starbucks",
  "equities_snap",
  "equities_block",
  "equities_tesla",
  "equities_uber",
  "equities_visa",
  "equities_exxonmobil",
  "equities_zoom"
];

// Any rewards that have deposits under £1 are not true user rewards
export const MINIMUM_REWARD_DEPOSIT = 100;

export type AssetRecentActivityRewardItemType = {
  type: "reward";
  item: RewardDocument & {
    isCancellable: boolean;
  };
};

type RewardAmountConfigType = {
  min: number;
  max: number;
  mean: number;
  variance: number;
};

type EuRewardAmountConfigType = {
  NO_REWARD: RewardAmountConfigType;
  ONE_REWARD: RewardAmountConfigType;
  OTHER: RewardAmountConfigType;
};

const REWARD_AMOUNT_CONFIG: Record<string, RewardAmountConfigType | EuRewardAmountConfigType> = {
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK]: {
    min: 5.07,
    max: 11.97,
    mean: 7.5,
    variance: 2.25
  },
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE]: {
    NO_REWARD: {
      min: 7.07,
      max: 25.0,
      mean: 20,
      variance: 2.75
    },
    ONE_REWARD: {
      min: 7.07,
      max: 22.0,
      mean: 17,
      variance: 2.75
    },
    OTHER: {
      min: 7.07,
      max: 18.05,
      mean: 12.5,
      variance: 2.75
    }
  },
  NON_GR_USERS: {
    min: 5.0,
    max: 12.0,
    mean: 7.5,
    variance: 2.25
  }
};

export default class RewardService {
  /**
   * PUBLIC METHODS
   */

  public static async createAdminReward(
    referrerEmail: string,
    referralEmail: string,
    targetUserEmail: string,
    asset: investmentUniverseConfig.AssetType,
    considerationAmount: string
  ): Promise<RewardDocument> {
    const isDoubleReward = referrerEmail && referralEmail;

    let referrer: UserDocument;
    let referral: UserDocument;

    if (isDoubleReward) {
      [referrer, referral] = await Promise.all([
        UserRepository.getUserByEmail(referrerEmail),
        UserRepository.getUserByEmail(referralEmail)
      ]);

      if (!referrer || !referral) {
        logger.error("Attempted to create rewards for non-existing user(s)", {
          module: "RewardService",
          method: "createAdminReward",
          data: {
            referrerEmail,
            referralEmail
          }
        });
        throw new InternalServerError(
          `Couldn't find referrer ${referrerEmail} or referral ${referralEmail}`,
          "Invalid Request"
        );
      }
    }

    const targetUser = await UserRepository.getUserByEmail(targetUserEmail);
    if (!targetUser) {
      logger.error("Attempted to create reward for non-existing user", {
        module: "rewardService",
        method: "createAdminReward",
        data: {
          targetUserEmail
        }
      });
      throw new InternalServerError(`Couldn't find target user ${targetUser}`, "Invalid Request");
    }

    const rewardData: RewardDTOInterface = {
      asset,
      isin: ASSET_CONFIG[asset as investmentUniverseConfig.AssetType].isin,
      consideration: {
        currency: targetUser.currency,
        amount: Decimal.mul(considerationAmount, 100).toNumber() // converting to cents
      },
      referrer: isDoubleReward && referrer.id,
      referral: isDoubleReward ? referral.id : targetUser.id,
      targetUser: targetUser.id,
      deposit: {
        activeProviders: ProviderService.getProviders(targetUser.companyEntity, [ProviderScopeEnum.BROKERAGE])
      },
      order: {
        activeProviders: ProviderService.getProviders(targetUser.companyEntity, [ProviderScopeEnum.BROKERAGE])
      }
    };

    logger.info(`Creating reward for target user ${targetUser.email} - admin reward`, {
      module: "RewardService",
      method: "createAdminReward"
    });

    return RewardService._createReward(rewardData);
  }

  /**
   * @description To create all CRM campaign rewards, the steps are the following:
   * 1. We retrieve all users that Mailchimp that are present in the "Pending Reward" segment in Mailchimp.
   * 2. For each eligible user, we create a single reward.
   */
  public static async createAllCRMCampaignRewards(): Promise<void> {
    await MailchimpService.executeForAllPendingRewardMembersPaginated(
      RewardService._createCRMCampaignRewardsForPage
    );
  }

  /**
   * @description Created rewards for EU whitelisted users without referrals
   */
  public static async createAllEuWhitelistRewards(): Promise<void> {
    await User.find({
      kycStatus: KycStatusEnum.PASSED,
      $or: [{ usedWhitelistCode: true }, { email: { $in: whitelistConfig.WHITELISTED_EU_EMAILS } }]
    })
      .cursor()
      .addCursorFlag("noCursorTimeout", true)
      .eachAsync(
        async (users: UserDocument[]) => {
          const rewardCreationPromises = users.map(async (user) => {
            try {
              await RewardService._processUserWhitelistReward(user);
            } catch (err) {
              captureException(err);
              logger.error(`Reward creation failed for ${user.email}`, {
                module: "RewardService",
                method: "createEuWhitelistedRewards",
                userEmail: user.email,
                data: { error: err }
              });
            }
          });

          await Promise.all(rewardCreationPromises);
        },
        { batchSize: 10 }
      );
  }

  /**
   * @description To create all referral rewards, the steps are the following:
   * 1. We retrieve all users that have:
   *    A) Signed up in the last 10 days.
   *    B) Have been referred by another user
   *    C) Have settled investments totalling at least £100
   *    D) Are not Whitelisted users (they follow different flow)
   * 2. Create a reward with a random amount/asset and assign it to the referred user
   * 3. Create a reward with a random amount/asset and assign it to the referrer (if they are eligible)
   */
  public static async createAllReferralRewards(): Promise<void> {
    const eligibleUsers = (
      (await UserService.getUsers({
        createdAfter: DateUtil.getDateOfDaysAgo(new Date(Date.now()), 10),
        kycStatus: KycStatusEnum.PASSED,
        referredOnly: true
      })) as ApiResponse<UserDocument>
    ).data.filter((user) => !user.usedEuWhitelistCodeOrEmail);

    for (let i = 0; i < eligibleUsers.length; i++) {
      const user = eligibleUsers[i];
      try {
        await RewardService._processUserReferralReward(user);
      } catch (err) {
        captureException(err);
        logger.error(`Reward creation failed for ${user.email}`, {
          module: "RewardService",
          method: "createAllReferralRewards",
          userEmail: user.email,
          data: { error: err }
        });
      }
    }
  }

  /**
   * @description Creates bonus payment requests to Wealthkernel for the rewards
   * that have been created but don't have payments requested yet.
   */
  public static async createRewardDeposits(): Promise<void> {
    // fetch reward documents
    const rewardsMissingDeposit = await Reward.find({
      "consideration.amount": { $gte: MINIMUM_REWARD_DEPOSIT },
      "deposit.activeProviders": ProviderEnum.WEALTHKERNEL,
      $or: [
        { "deposit.providers.wealthkernel.id": { $exists: false } },
        { "deposit.providers.wealthkernel.id": { $eq: undefined } }
      ],
      accepted: true
    }).populate([
      {
        path: "targetUser",
        populate: {
          path: "portfolios"
        }
      }
    ]);

    for (let i = 0; i < rewardsMissingDeposit.length; i++) {
      const reward = rewardsMissingDeposit[i];
      await RewardService._createRewardDeposit(reward);
    }
  }

  /**
   * @description Creates order requests to Wealthkernel for the reward ETFs, when
   * the corresponding transaction deposit has been settled and enough cash is available
   * to the user's e-wallet.
   */
  public static async createRewardOrders(): Promise<void> {
    // Only submit reward orders in realtime during stock market hours
    if (!SubmissionWindowUtil.isCurrentTimeWithinRealtimeSubmissionWindow("stock")) {
      logger.info("Will not create WK reward orders, as we are not within stock realtime submission windows", {
        module: "RewardService",
        method: "createRewardOrders"
      });
      return;
    }

    // fetch reward documents
    const rewardsPendingOrder: RewardDocument[] = await Reward.find({
      "deposit.providers.wealthkernel.status": { $eq: "Settled" },
      "order.activeProviders": ProviderEnum.WEALTHKERNEL,
      $or: [
        { "order.providers.wealthkernel.id": { $exists: false } },
        { "order.providers.wealthkernel.id": { $eq: undefined } }
      ]
    }).populate([
      {
        path: "targetUser",
        populate: {
          path: "portfolios"
        }
      }
    ]);

    const investmentProducts = await InvestmentProductService.getInvestmentProductsDict("commonId", false);

    const rewards = rewardsPendingOrder.filter((reward) => investmentProducts[reward.asset].buyLine.active);

    for (let i = 0; i < rewards.length; i++) {
      const reward = rewards[i];
      await RewardService._createRewardOrder(reward);

      const isFirstInvestment = await UserService.userHasSingleInvestment(reward.targetUser.id);

      if (isFirstInvestment) {
        eventEmitter.emit(events.transaction.firstInvestmentCreation.eventId, reward.targetUser);
      }

      const { consideration, fees } = reward;
      const transactionInfo: TrackTransactionInfoType = consideration?.amount
        ? {
            side: "buy",
            category: "reward",
            assetName: ASSET_CONFIG[reward.asset].simpleName,
            amount: new Decimal(consideration.amount).div(100).toNumber(),
            currency: consideration.currency,
            fxFees: fees?.fx?.amount ?? 0,
            commissionFees: fees?.commission?.amount ?? 0,
            executionSpreadFees: fees?.executionSpread?.amount ?? 0
          }
        : {};

      eventEmitter.emit(events.transaction.investmentCreation.eventId, reward.targetUser, {
        isFirst: isFirstInvestment,
        ...transactionInfo
      } as TrackPropertiesType);
    }
  }

  public static async getSettledRewards(ownerId: Types.ObjectId): Promise<RewardDocument[]> {
    return Reward.find({ targetUser: ownerId, status: "Settled" });
  }

  public static async getReward(rewardId: string): Promise<RewardDocument> {
    if (!rewardId || !validator.isMongoId(rewardId)) {
      throw new InternalServerError(`Reward id ${rewardId} is not valid`);
    }

    return Reward.findOne({ _id: rewardId }).populate("referrer referral targetUser");
  }

  public static async getRewards(
    filter: RewardsFilter = {},
    pageConfig?: { page: number; pageSize: number },
    populate = { portfolio: true },
    sort = "-createdAt"
  ) {
    return pageConfig
      ? RewardService._getRewardsPaginated(filter, pageConfig, populate, sort)
      : { data: await RewardService._getRewards(filter, populate, sort) };
  }

  /**
   * @description Syncs the pending deposits transactions that are made
   * from the Wealthyhood wallet to the investors' e-wallet to have available cash
   * that will later be used to buy the reward ETF.
   */
  public static async syncPendingRewardDeposits(): Promise<void> {
    // For rewards with a wk deposit id, sync wk status
    // that were createdAt at least 15 minutes ago, to act as a fallback to webhook mechanism
    const minimumCreationTime = new Date(Date.now() - 15 * 60 * 1000);

    // fetch reward documents
    const rewardsPendingDeposits: RewardDocument[] = await Reward.find({
      "deposit.providers.wealthkernel.id": { $exists: true, $ne: null },
      "deposit.providers.wealthkernel.status": { $ne: "Settled" },
      createdAt: { $lte: minimumCreationTime }
    }).populate("targetUser");

    for (let i = 0; i < rewardsPendingDeposits.length; i++) {
      const reward = rewardsPendingDeposits[i];
      const user = reward.targetUser as UserDocument;
      try {
        const wkBonus = await ProviderService.getBrokerageService(user.companyEntity).retrieveBonus(
          reward.deposit.providers.wealthkernel.id
        );
        await RewardService.updateRewardDepositStatus(reward, wkBonus.status);
      } catch (err) {
        captureException(err);
        logger.error(`Reward deposit syncing failed for ${reward._id}`, {
          module: "RewardService",
          method: "syncPendingRewardDeposits",
          data: { rewardId: reward._id, bonus: reward.deposit?.providers?.wealthkernel?.id, error: err }
        });
      }
    }
  }

  /**
   * @description Syncs the matched ETF orders to settle the rewards and update the portfolio holdings
   * with the rewarded asset.
   */
  public static async syncPendingRewards(): Promise<void> {
    const pendingRewardsWithMatchedOrder = await Reward.find({
      status: "Pending",
      "order.providers.wealthkernel.id": { $exists: true, $ne: null },
      "order.providers.wealthkernel.status": { $eq: "Matched" }
    });

    for (let i = 0; i < pendingRewardsWithMatchedOrder.length; i++) {
      const reward = pendingRewardsWithMatchedOrder[i];
      try {
        await RewardService._syncPendingReward(reward);
      } catch (err) {
        captureException(err);
        logger.error(`Pending reward syncing failed for ${reward._id}`, {
          module: "RewardService",
          method: "syncPendingRewardOrders",
          data: { rewardId: reward._id, error: err }
        });
      }
    }
  }

  public static async updateReward(
    rewardId: string,
    rewardData: { depositId?: string; hasViewedAppModal?: boolean; accepted?: boolean }
  ): Promise<void> {
    if (!rewardId || !validator.isMongoId(rewardId)) {
      throw new InternalServerError(`Reward id ${rewardId} is not valid`);
    }

    const sanitisedData = Object.fromEntries(
      Object.entries(rewardData).filter(([, value]) => value !== undefined && value !== null)
    );

    if (!Object.keys(sanitisedData).length) {
      throw new InternalServerError("Data cannot be empty");
    }

    const { depositId } = rewardData;
    if (depositId) {
      sanitisedData["deposit.providers.wealthkernel.id"] = depositId;
    }

    await Reward.findOneAndUpdate({ _id: rewardId }, sanitisedData);
  }

  public static async getRewardByDepositId(depositId: string): Promise<RewardDocument> {
    return await Reward.findOne({
      "deposit.providers.wealthkernel.id": depositId
    });
  }

  public static async getRewardByOrderId(orderId: string): Promise<RewardDocument> {
    return await Reward.findOne({
      "order.providers.wealthkernel.id": orderId
    });
  }

  public static async getUserActivityRewards(userId: string, limit?: number) {
    const rewardQuery = {
      targetUser: userId,
      status: "Settled"
    };

    const rewards = await Reward.find(rewardQuery)
      .sort({ createdAt: -1 }) // Sorting in descending order by createdAt
      .limit(limit || 0) // Limiting the number of results to 5
      .exec();

    return rewards;
  }

  public static async updateRewardDepositStatus(
    reward: RewardDocument,
    newDepositStatus: BonusStatusType
  ): Promise<void> {
    if (newDepositStatus === "Settled") {
      await Reward.findOneAndUpdate({ _id: reward.id }, { "deposit.providers.wealthkernel.status": "Settled" });
    } else if (newDepositStatus === "Rejected") {
      logger.error(`Reward bonus payment was rejected for ${reward.id}`, {
        module: "RewardService",
        method: "updateRewardDepositStatus"
      });

      captureMessage(`Reward bonus payment was rejected`, {
        level: "error",
        extra: {
          rewardId: reward.id
        }
      });
    }
  }

  public static async updateRewardOrderStatus(
    reward: RewardDocument,
    newOrderStatus: WealthkernelOrderStatusWithSettledType
  ): Promise<void> {
    if (OrderService.isOrderWkStatusTerminal(reward.order?.providers?.wealthkernel?.status)) {
      logger.warn(
        `Cannot update WK reward order as it is already ${reward.order?.providers?.wealthkernel?.status}`,
        {
          module: "RewardService",
          method: "updateRewardOrderStatus",
          data: {
            reward
          }
        }
      );
      return;
    }

    logger.info(`Syncing reward order for reward ${reward.id}`, {
      module: "RewardService",
      method: "updateRewardOrderStatus"
    });

    const owner = await UserService.getUser(reward.targetUser.toString(), {
      addresses: false,
      portfolios: true,
      subscription: true
    });

    const wkOrderData = await ProviderService.getBrokerageService(owner.companyEntity).retrieveOrder(
      reward.order?.providers?.wealthkernel?.id
    );
    if (!wkOrderData) {
      throw new Error(`Reward order for reward ${reward.id} could not be retrieved from Wealthkernel.`);
    } else if (wkOrderData.status !== newOrderStatus) {
      throw new Error(
        `Received WK reward order webhook with status ${newOrderStatus}, but order in WK is ${wkOrderData.status}`
      );
    }

    if (newOrderStatus === "Matched") {
      const quantity = OrderService.calculateMatchedOrderQuantity(wkOrderData);
      const unitPrice = OrderService.calculateMatchedOrderUnitPrice(wkOrderData);
      const marketSettledAt = OrderService.calculateMatchedOrderMarketSettledAt(wkOrderData);

      // If the user doesn't have a subscription but is receiving a reward as a verified user, we use the free plan to determine
      // the FX fee.
      const plan = (owner.subscription as SubscriptionDocument)?.plan ?? "free";
      const investmentProduct = await InvestmentProductService.getInvestmentProduct(reward.asset, false);

      // Extract broker FX data from the first fill (similar to OrderService)
      const fills = wkOrderData.fills?.filter((fill) => fill.status === "Matched") || [];
      const brokerFxRate = fills.length > 0 ? fills[0].exchangeRate : undefined;
      const baseExchangeRate = fills.length > 0 ? fills[0].baseExchangeRate : undefined;
      const displayFxFee = calculateDisplayFxFee({
        considerationAmount: OrderService.calculateMatchedOrderAmount(wkOrderData),
        plan
      });

      // Calculate broker FX fee if we have the necessary data
      const accountingBrokerFxFee = calculateBrokerFxFee({
        considerationAmount: new Decimal(reward.consideration.amount).div(100).toNumber(),
        plan,
        brokerFxRate
      });

      // We only store the exchange rate if the order is foreign currency traded.
      let exchangeRate: number;
      if (investmentProduct.tradedCurrency !== owner.currency) {
        exchangeRate = await OrderService.calculateMatchedOrderExchangeRateWithSpread(wkOrderData, plan);
      }

      await Reward.findOneAndUpdate(
        { _id: reward.id },
        {
          updatedAt: new Date(),
          exchangeRate,
          displayFxFee,
          quantity,
          unitPrice,
          marketSettledAt,
          filledAt: fills.length > 0 ? fills[0].filledAt : undefined,
          "order.providers.wealthkernel.status": "Matched",
          "order.providers.wealthkernel.brokerFxRate": brokerFxRate,
          "order.providers.wealthkernel.baseExchangeRate": baseExchangeRate,
          "order.providers.wealthkernel.accountingBrokerFxFee": accountingBrokerFxFee
        }
      );

      // We want to clear the cache to avoid displaying the change in holdings as increase in returns.
      // MWRR & value will be cached when portfolio returns are requested again through dashboard (or at cron).
      const portfolioId = owner.portfolios[0].id;

      await Promise.all([
        RedisClientService.Instance.del(`portfolios:mwrr:${portfolioId}`),
        RedisClientService.Instance.del(`portfolios:value_at_mwrr:${portfolioId}`),
        RedisClientService.Instance.del(`portfolios:up_by:${portfolioId}`),
        RedisClientService.Instance.del(`portfolios:value_at_up_by:${portfolioId}`)
      ]);
    } else if (newOrderStatus === "Rejected") {
      logger.error(`Reward order was rejected for ${reward.id}`, {
        module: "RewardService",
        method: "updateRewardOrderStatus"
      });

      eventEmitter.emit(events.order.orderRejection.eventId, owner, {
        asset: reward.asset,
        amount: Decimal.div(reward.consideration?.amount, 100).toNumber(),
        currency: reward.consideration?.currency ?? owner.currency,
        side: "Buy",
        rejectionReason: wkOrderData.reason
      });
    }
  }

  public static async getAssetRecentActivityRewards(
    userId: string,
    assetId: investmentUniverseConfig.AssetType,
    limit?: number
  ): Promise<AssetRecentActivityRewardItemType[]> {
    const assetRewards = limit
      ? (
          (await RewardService.getRewards(
            { targetUser: userId, assetId: assetId, status: "Settled" },
            { page: 1, pageSize: limit }
          )) as PaginatedRewardsResponse
        ).rewards
      : (
          (await RewardService.getRewards({ targetUser: userId, assetId: assetId, status: "Settled" })) as {
            data: RewardDocument[];
          }
        ).data;

    return assetRewards.map(
      (reward) =>
        ({
          type: "reward",
          item: { ...reward.toObject(), isCancellable: false }
        }) as AssetRecentActivityRewardItemType
    );
  }

  /**
   * PRIVATE METHODS
   */
  private static async _createCRMCampaignRewardsForPage(users: { email: string }[]): Promise<void> {
    for (let i = 0; i < users.length; i++) {
      const user = users[i];

      try {
        const targetUser = await UserRepository.getUserByEmail(user.email);
        if (!targetUser) {
          logger.info(`Not creating reward for ${user.email} as they're not signed up!`, {
            module: "RewardService",
            method: "_createCRMCampaignRewardsForPage",
            userEmail: user.email
          });
          continue;
        }
        await RewardService._processUserCRMCampaignReward(targetUser);
      } catch (err) {
        captureException(err);
        logger.error(`Reward creation failed for ${user.email}`, {
          module: "RewardService",
          method: "_createCRMCampaignRewardsForPage",
          userEmail: user.email,
          data: { error: err }
        });
      }
    }
  }

  private static _createRewardsDbFilter(filter: RewardsFilter) {
    const dbFilter = {
      asset: filter.assetId,
      targetUser: filter.targetUser,
      referral: filter.referral,
      hasViewedAppModal: filter.hasViewedAppModal,
      status: filter.status,
      "order.providers.wealthkernel.status": filter.orderStatus,
      unrestrictedAt: null as any,
      updatedAt: null as any,
      createdAt: null as any,
      "order.providers.wealthkernel.submittedAt": filter.orderSubmissionDay
        ? { $gte: filter.orderSubmissionDay, $lt: DateUtil.getDateAfterNdays(filter.orderSubmissionDay, 1) }
        : null,
      accepted: filter.accepted
    };

    if (filter.creationDate) {
      dbFilter["createdAt"] = {
        $gte: filter.creationDate.startDate ?? new Date("1970-01-01T00:00:00Z"),
        $lt: filter.creationDate.endDate ?? new Date(Date.now())
      };
    }

    if (filter.restrictedOnly) {
      dbFilter["unrestrictedAt"] = { $gt: new Date() };
    }

    if (filter.updatedDate) {
      dbFilter["updatedAt"] = {
        $gte: filter.updatedDate.startDate,
        $lt: filter.updatedDate.endDate
      };
    }

    if (filter.orderStatus) {
      dbFilter["order.providers.wealthkernel.status"] = filter.orderStatus;
    }

    return dbFilter
      ? Object.fromEntries(Object.entries(dbFilter).filter(([, value]) => value !== undefined && value !== null))
      : {};
  }

  private static async _getRewards(
    filter?: RewardsFilter,
    populate?: { portfolio?: boolean },
    sort?: string
  ): Promise<RewardDocument[]> {
    const dbFilter = this._createRewardsDbFilter(filter);

    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    const rewards = await Reward.find(dbFilter, null, options);

    await RewardService._doPopulations(populate, rewards);

    return rewards;
  }

  private static async _getRewardsPaginated(
    filter: RewardsFilter = {},
    pageConfig: { page: number; pageSize: number },
    populate?: { portfolio?: boolean },
    sort?: string
  ): Promise<PaginatedRewardsResponse> {
    const dbFilter = this._createRewardsDbFilter(filter);
    const count = await Reward.countDocuments(dbFilter);
    const pageConfigToUse = PaginationUtil.getPaginationParametersFor(count, pageConfig);

    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    const rewards = await Reward.find(dbFilter, null, options)
      .skip((pageConfigToUse.page - 1) * pageConfigToUse.pageSize)
      .limit(pageConfigToUse.pageSize);

    await RewardService._doPopulations(populate, rewards);

    return { pagination: pageConfigToUse, rewards };
  }

  private static async _doPopulations(populate: { portfolio?: boolean }, rewards: RewardDocument[]) {
    if (populate?.portfolio) {
      await Promise.all(
        rewards.map((reward) =>
          reward.populate({
            path: "targetUser",
            populate: {
              path: "portfolios"
            }
          })
        )
      );
    }
  }

  private static async _createRewardDeposit(reward: RewardDocument): Promise<void> {
    logger.info(`Creating reward bonus for doc ${reward.id}`, {
      module: "RewardService",
      method: "_createRewardDeposit"
    });

    await DbUtil.populateIfNotAlreadyPopulated(reward, RewardPopulationFieldsEnum.TARGET_USER);
    const targetUser = reward.targetUser as UserDocument;

    const generalInvestmentPortfolio = await PortfolioService.getGeneralInvestmentPortfolio(targetUser);

    if (!generalInvestmentPortfolio.providers?.wealthkernel?.id) {
      logger.info(`Not creating reward bonus for doc ${reward.id} as the portfolio is not submitted to broker`, {
        module: "RewardService",
        method: "_createRewardDeposit"
      });
      return;
    }

    const bonusPayment = await ProviderService.getBrokerageService(targetUser.companyEntity).createBonus({
      destinationPortfolio: generalInvestmentPortfolio.providers?.wealthkernel?.id,
      consideration: {
        currency: MainCurrencyToWealthkernelCurrency[targetUser.currency],
        amount: Decimal.div(reward.consideration.bonusAmount, 100).toNumber()
      },
      clientReference: reward.id
    });

    logger.info(`Created reward bonus for doc ${reward.id} with WK id ${bonusPayment.id}`, {
      module: "RewardService",
      method: "_createRewardDeposit"
    });

    await Reward.findByIdAndUpdate(reward.id, {
      "deposit.providers.wealthkernel": {
        status: "Created",
        id: bonusPayment.id,
        submittedAt: new Date(Date.now())
      }
    });
  }

  private static async _createRewardOrder(reward: RewardDocument): Promise<void> {
    await DbUtil.populateIfNotAlreadyPopulated(reward, RewardPopulationFieldsEnum.TARGET_USER);
    const targetUser = reward.targetUser as UserDocument;
    const generalInvestmentPortfolio = await PortfolioService.getGeneralInvestmentPortfolio(targetUser);

    // 1. Create WK order
    const wkOrderData = {
      portfolioId: generalInvestmentPortfolio.providers?.wealthkernel?.id,
      isin: investmentUniverseConfig.ASSET_CONFIG[reward.asset].isin,
      settlementCurrency: MainCurrencyToWealthkernelCurrency[targetUser.currency],
      side: "Buy" as OrderSideType,
      consideration: {
        currency: MainCurrencyToWealthkernelCurrency[targetUser.currency],
        amount: Decimal.div(reward.consideration.orderAmount, 100).toNumber()
      }
    };

    const wealthkernelResponse = await ProviderService.getBrokerageService(targetUser.companyEntity).createOrder(
      wkOrderData,
      reward.id,
      { realtime: true }
    );
    const orderWealthkernelId = wealthkernelResponse.id;

    // 2. Update order in DB with wealthkernel info
    await Reward.findOneAndUpdate(
      {
        _id: reward.id
      },
      {
        "order.providers.wealthkernel": {
          status: "Pending",
          id: orderWealthkernelId,
          submittedAt: new Date(Date.now())
        }
      }
    );
  }

  private static async _syncPendingReward(reward: RewardDocument): Promise<void> {
    if (reward.orderStatus !== "Settled") {
      return;
    }

    await reward.populate([
      {
        path: "targetUser",
        populate: {
          path: "portfolios"
        }
      },
      { path: "referrer" }
    ]);
    const targetUser = reward.targetUser as UserDocument;

    // TODO: not removing those lines, they will be used to handle reward remainders.
    // const wkOrder = await ProviderService.getBrokerageService(targetUser.companyEntity).retrieveOrder(
    //   reward.order.providers.wealthkernel.id
    // );

    const isFirst = await UserService.userHasNoSettledInvestments(targetUser);

    await DbUtil.runInSession(async (session: mongoose.ClientSession) => {
      await Reward.findOneAndUpdate(
        { _id: reward.id },
        {
          status: "Settled"
        },
        { session }
      );

      const { consideration, fees } = reward;
      const transactionInfo: TrackTransactionInfoType = consideration?.amount
        ? {
            side: "buy",
            category: "reward",
            assetName: ASSET_CONFIG[reward.asset].simpleName,
            amount: new Decimal(consideration.amount).div(100).toNumber(),
            currency: consideration.currency,
            fxFees: fees?.fx?.amount ?? 0,
            commissionFees: fees?.commission?.amount ?? 0
          }
        : {};

      // FIXME: this event should not be emitted for rewards.
      // The reason why it was initially implemented this way is because we wanted to track rewards as investments on Mixpanel.
      // However, regardless of what we sent to Mixpanel (or other 3rd party tools), we should be emitting the correct event internally.
      // (This means that we may have some code duplication for rewards on the event handler).
      eventEmitter.emit(
        events.transaction.investmentSuccess.eventId,
        targetUser,
        {
          isFirst,
          ...transactionInfo
        },
        {
          type: "reward"
        }
      );

      // Update user portfolio holdings and cash
      const portfolio = await PortfolioService.getGeneralInvestmentPortfolio(reward.targetUser as UserDocument);
      await PortfolioService.updatePortfolioHoldings(
        portfolio.id,
        [
          {
            side: "Buy",
            quantity: reward.quantity,
            isin: reward.isin
          }
        ],
        { session }
      );

      // TODO: not removing those lines, they will be used to handle reward remainders.
      // const settledAmount = Decimal.mul(OrderService.calculateMatchedOrderAmount(wkOrder), 100);
      // const uninvestedCash = new Decimal(reward.consideration.amount).sub(settledAmount);

      // await PortfolioService.updateCashAvailability(portfolio.id, targetUser.currency, uninvestedCash.toNumber(), {
      //   session,
      //   available: true,
      //   settled: true
      // });

      // Set the user's portfolioConversionStatus to 'completed'
      await UserService.convertUser(targetUser, "completed", { session });
    });

    eventEmitter.emit(events.referral.rewardSettled.eventId, targetUser, { reward });
  }

  private static async _processUserCRMCampaignReward(targetUser: UserDocument): Promise<void> {
    const existingUserRewards = await RewardService._getRewards({
      targetUser: targetUser.id,
      referral: targetUser.id
    });
    if (existingUserRewards.length > 0) {
      logger.info(`Target user ${targetUser.id} already has pending rewards, aborting...`, {
        module: "RewardService",
        method: "_processUserCRMCampaignReward",
        userEmail: targetUser.email
      });

      return;
    }

    const isTargetUserEligible = await UserService.isRewardEligible(targetUser, {
      targetUserEmail: targetUser.email,
      referral: targetUser.id
    });

    if (isTargetUserEligible) {
      const amount = await RewardService._getRandomRewardAmount(targetUser);
      const asset = RewardService._getRandomRewardAsset();

      const reward = {
        asset,
        isin: ASSET_CONFIG[asset].isin,
        consideration: {
          currency: targetUser.currency,
          amount
        },
        referralCampaign: "CRM Campaign",
        referral: targetUser.id,
        targetUser: targetUser.id,
        deposit: {
          activeProviders: ProviderService.getProviders(targetUser.companyEntity, [ProviderScopeEnum.BROKERAGE])
        },
        order: {
          activeProviders: ProviderService.getProviders(targetUser.companyEntity, [ProviderScopeEnum.BROKERAGE])
        }
      };

      logger.info(`Creating reward for target user ${targetUser.email} - crm campaign`, {
        module: "RewardService",
        method: "_processUserCRMCampaignReward"
      });

      await RewardService._createReward(reward);

      eventEmitter.emit(events.referral.referralRewardCreation.eventId, targetUser);

      await MailchimpService.updateMember(
        targetUser.email,
        {
          merge_fields: { CAMPLIVE: "Reward settled" }
        },
        AudienceIdEnum.WEALTHYHOOD,
        { silent: true }
      );
    }
  }

  private static async _processUserReferralReward(referral: UserDocument): Promise<void> {
    const existingUserRewards = await RewardService._getRewards({
      targetUser: referral.id,
      referral: referral.id
    });
    if (existingUserRewards.length > 0) {
      logger.info(`Referred user ${referral.id} already has reward, not processing their referral reward...`, {
        module: "RewardService",
        method: "_processUserReferralReward",
        userEmail: referral.email
      });

      return;
    }

    const referrer = await UserService.getUserByEmailIncludingDeleted(
      referral.referredByEmail,
      { includeDeleted: true },
      { participant: true }
    );

    const isReferredUserEligible = await UserService.isRewardEligible(referral, {
      targetUserEmail: referral.email,
      referral: referral.id,
      referrer: referrer?.id ?? referral.referredByEmail
    });

    if (isReferredUserEligible) {
      const amount = await RewardService._getRandomRewardAmount(referral);
      const asset = RewardService._getRandomRewardAsset();

      const reward: RewardDTOInterface = {
        asset,
        isin: ASSET_CONFIG[asset].isin,
        consideration: {
          currency: referral.currency,
          amount
        },
        referral: referral.id,
        targetUser: referral.id,
        deposit: {
          activeProviders: ProviderService.getProviders(referral.companyEntity, [ProviderScopeEnum.BROKERAGE])
        },
        order: {
          activeProviders: ProviderService.getProviders(referral.companyEntity, [ProviderScopeEnum.BROKERAGE])
        }
      };

      if (referrer) {
        reward.referrer = referrer.id;
      } else {
        reward.referralCampaign = referral.referredByEmail;
      }

      logger.info(`Creating reward for referred user ${referral.email} - referral`, {
        module: "RewardService",
        method: "_processUserReferralReward"
      });

      await RewardService._createReward(reward);

      eventEmitter.emit(events.referral.referralRewardCreation.eventId, referral);

      if (referrer && !referrer?.participant?.isAmbassador) {
        const isReferrerEligible = await UserService.isRewardEligible(referrer, {
          targetUserEmail: referrer.email,
          referral: referral.id,
          referrer: referrer.id
        });

        if (isReferrerEligible) {
          const amount = await RewardService._getRandomRewardAmount(referrer);
          const asset = RewardService._getRandomRewardAsset();

          const reward = {
            asset,
            isin: ASSET_CONFIG[asset].isin,
            consideration: {
              currency: referrer.currency,
              amount
            },
            referrer: referrer.id,
            referral: referral.id,
            targetUser: referrer.id,
            deposit: {
              activeProviders: ProviderService.getProviders(referrer.companyEntity, [ProviderScopeEnum.BROKERAGE])
            },
            order: {
              activeProviders: ProviderService.getProviders(referrer.companyEntity, [ProviderScopeEnum.BROKERAGE])
            }
          };

          logger.info(`Creating reward for referrer ${referrer.email} - referral`, {
            module: "RewardService",
            method: "_processUserReferralReward"
          });

          await RewardService._createReward(reward);

          eventEmitter.emit(events.referral.referrerRewardCreation.eventId, referrer);
        }
      }
    }
  }

  private static async _processUserWhitelistReward(targetUser: UserDocument): Promise<void> {
    const existingUserRewards = await RewardService._getRewards({
      targetUser: targetUser.id
    });
    if (existingUserRewards.length > 0) {
      logger.info(`Target user ${targetUser.id} already has reward, not processing their reward...`, {
        module: "RewardService",
        method: "_processUserWhitelistReward",
        userEmail: targetUser.email
      });

      return;
    }

    const isReferredUserEligible = await UserService.isRewardEligible(targetUser, {
      targetUserEmail: targetUser.email,
      referral: targetUser.id
    });

    if (isReferredUserEligible) {
      const amount = await RewardService._getRandomRewardAmount(targetUser);
      const asset = RewardService._getRandomRewardAsset();

      const reward: RewardDTOInterface = {
        asset,
        isin: ASSET_CONFIG[asset].isin,
        consideration: {
          currency: targetUser.currency,
          amount
        },
        referral: targetUser.id,
        targetUser: targetUser.id,
        referralCampaign: "Whitelisted EU Users",
        deposit: {
          activeProviders: ProviderService.getProviders(targetUser.companyEntity, [ProviderScopeEnum.BROKERAGE])
        },
        order: {
          activeProviders: ProviderService.getProviders(targetUser.companyEntity, [ProviderScopeEnum.BROKERAGE])
        }
      };

      logger.info(`Creating reward for target user ${targetUser.email} - whitelist`, {
        module: "RewardService",
        method: "_processUserWhitelistReward"
      });

      await RewardService._createReward(reward);

      eventEmitter.emit(events.referral.referralRewardCreation.eventId, targetUser);
    }
  }

  private static async _createReward(rewardData: RewardDTOInterface): Promise<RewardDocument> {
    const targetUser = await UserService.getUser(rewardData.targetUser.toString(), {
      addresses: false,
      portfolios: false,
      subscription: true
    });
    const currency = targetUser.currency;

    // If the target user does not have a subscription, we treat them as a free user
    const PRICE_CONFIG = ConfigUtil.getPricing(targetUser.companyEntity);
    const plan = PRICE_CONFIG[(targetUser.subscription as SubscriptionDocument)?.price ?? "free_monthly"].plan;
    const executionSpreadFee = new Decimal(rewardData.consideration.amount)
      .div(100)
      .mul(EXECUTION_SPREAD_RATES[plan])
      .toDecimalPlaces(2);

    // Calculate total fee amount in cents
    const totalFeeAmountInCents = Decimal.add(MINIMUM_FX_FEE, MINIMUM_COMMISSION_FEE)
      .add(executionSpreadFee)
      .mul(100)
      .toNumber();

    // Calculate bonusAmount (consideration amount + total fees)
    const bonusAmount = Decimal.add(rewardData.consideration.amount, totalFeeAmountInCents).toNumber();

    // orderAmount should be the same as the original consideration amount
    const orderAmount = rewardData.consideration.amount;

    const reward = await new Reward({
      ...rewardData,
      consideration: {
        amount: rewardData.consideration.amount,
        bonusAmount,
        orderAmount,
        currency
      },
      fees: {
        fx: { amount: MINIMUM_FX_FEE, currency },
        commission: { amount: MINIMUM_COMMISSION_FEE, currency },
        executionSpread: { amount: executionSpreadFee, currency }
      },
      // initially auto-accept was implemented for EU users only, but
      // for simplicity we'll apply it to everyone
      accepted: true
    }).save();

    logger.info(`Created reward for user ${rewardData.targetUser}`, {
      module: "RewardService",
      method: "createReward"
    });

    return reward;
  }

  /**
   * @description
   * Generates a random reward amount (in cents) based on the user's residency country and company entity.
   * For non-GR users: Uses mean 7.5 with minimum 5.0
   * For UK users: Uses standard UK configuration
   * For EU users: Adjusts mean based on existing reward count (0 rewards: mean=20, 1 reward: mean=17, 2+ rewards: default config)
   * @param targetUser The user document
   * @returns The reward amount in cents (integer)
   * @private
   */
  private static async _getRandomRewardAmount(targetUser: UserDocument): Promise<number> {
    const companyEntity = targetUser.companyEntity;
    if (companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK) {
      return RewardService._getRandomRewardAmountUK();
    } else if (targetUser.residencyCountry !== "GR") {
      return RewardService._getRandomRewardAmountNonGR();
    } else {
      return RewardService._getRandomRewardAmountGR(targetUser.id);
    }
  }

  /**
   * @description
   * Generates a random reward amount (in cents) from a given configuration.
   * The amount is sampled from a normal (gaussian) distribution and clamped to the min/max range.
   * @param config The reward amount configuration containing min, max, mean, and variance
   * @returns The reward amount in cents (integer)
   * @private
   */
  private static _generateRandomAmountFromConfig(config: RewardAmountConfigType): number {
    let amount = new Decimal(0);
    while (amount.lessThan(config.min) || amount.greaterThan(config.max)) {
      amount = new Decimal(gaussian(config.mean, config.variance).random(1)[0]);
    }
    return amount.mul(100).floor().toNumber(); // Convert to cents
  }

  /**
   * @description
   * Generates a random reward amount (in cents) for UK users.
   * @returns The reward amount in cents (integer)
   * @private
   */
  private static _getRandomRewardAmountUK(): number {
    const config = REWARD_AMOUNT_CONFIG[entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK] as RewardAmountConfigType;
    return RewardService._generateRandomAmountFromConfig(config);
  }

  /**
   * @description
   * Generates a random reward amount (in cents) for users not from Greece (GR).
   * Uses mean: 7.5, min: 5.0, max: 12.0, variance: 2.25
   * @returns The reward amount in cents (integer)
   * @private
   */
  private static _getRandomRewardAmountNonGR(): number {
    const config = REWARD_AMOUNT_CONFIG.NON_GR_USERS as RewardAmountConfigType;
    return RewardService._generateRandomAmountFromConfig(config);
  }

  /**
   * @description
   * Generates a random reward amount (in cents) for EU users.
   * The amount is sampled from a normal (gaussian) distribution and clamped to the min/max range defined in REWARD_AMOUNT_CONFIG.
   * The mean is adjusted based on the number of existing rewards for the user:
   * @param targetUserId The ID of the target user
   * @returns The reward amount in cents (integer)
   * @private
   */
  private static async _getRandomRewardAmountGR(targetUserId: string): Promise<number> {
    const euConfig = REWARD_AMOUNT_CONFIG[entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE] as {
      NO_REWARD: { min: number; max: number; mean: number; variance: number };
      ONE_REWARD: { min: number; max: number; mean: number; variance: number };
      OTHER: { min: number; max: number; mean: number; variance: number };
    };

    // Get existing rewards count for the user
    const existingRewards = await RewardService._getRewards({
      targetUser: targetUserId
    });

    let config;
    if (existingRewards.length === 0) {
      config = euConfig.NO_REWARD;
    } else if (existingRewards.length === 1) {
      config = euConfig.ONE_REWARD;
    } else {
      config = euConfig.OTHER;
    }

    return RewardService._generateRandomAmountFromConfig(config);
  }

  /**
   * @description For order management reasons, we don't want to give out more
   * than one asset per day. Therefore, depending on the date we determine what
   * asset to give out by modding it with the length of assets i.e. on the 25th
   * we're going to give out asset with index 25 % 5 = 0 -> equities_global
   * @private
   */
  private static _getRandomRewardAsset(): investmentUniverseConfig.AssetType {
    return REWARD_ASSETS[new Decimal(new Date(Date.now()).getDate()).mod(REWARD_ASSETS.length).toNumber()];
  }
}
